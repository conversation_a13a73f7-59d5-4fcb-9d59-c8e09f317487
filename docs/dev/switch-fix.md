# Switch Toggle Component Fix

## Issue Description
The SwitchToggle component was displaying abnormally with the text "Compare with same time 7d ago" not rendering properly.

## Root Cause
1. **Incorrect Props Interface**: The component was using generic input props without proper typing
2. **Missing Event Handler**: The `onChange` prop was not properly typed for boolean state changes
3. **Text Wrapping**: Long labels were not handled properly

## Solution Applied

### 1. Updated Component Interface
```typescript
const SwitchToggle = React.forwardRef<
  HTMLInputElement,
  Omit<React.ComponentPropsWithoutRef<"input">, "onChange"> & {
    label?: string
    checked?: boolean
    onCheckedChange?: (checked: boolean) => void
  }
>
```

### 2. Proper Event Handling
```typescript
<input 
  type="checkbox" 
  className="peer hidden" 
  ref={ref}
  checked={checked}
  onChange={(e) => onCheckedChange?.(e.target.checked)}
  {...props}
/>
```

### 3. Text Layout Fix
```typescript
{label && <span className="text-sm text-foreground whitespace-nowrap">{label}</span>}
```

### 4. Updated Usage
```typescript
<SwitchToggle 
  checked={compare} 
  onCheckedChange={setCompare}  // Changed from onChange
  label={t("compare7d")} 
/>
```

## Key Improvements
- ✅ **Proper TypeScript typing**: Eliminates type errors
- ✅ **Correct event handling**: Boolean state changes work properly
- ✅ **Better text rendering**: Added `whitespace-nowrap` for long labels
- ✅ **Consistent API**: Follows React component patterns

## Testing
- Switch toggles correctly between on/off states
- Label text displays properly without wrapping issues
- No TypeScript compilation errors
- Baseline comparison functionality works as expected

## Result
The switch component now displays correctly with proper text layout and functional toggle behavior.
